// sw.js - Service Worker for Math Game

// Import Workbox libraries
importScripts('https://storage.googleapis.com/workbox-cdn/releases/7.3.0/workbox-sw.js');

// Check if workbox is loaded
if (workbox) {
  console.log('Yay! Workbox is loaded 🎉');
  
  // Enable debugging in development
  workbox.setConfig({
    debug: false
  });
  
  // Cache static assets with stale-while-revalidate strategy
  workbox.routing.registerRoute(
    ({request}) => request.destination === 'image',
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: 'images',
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 Days
        }),
      ],
    })
  );
  
  // Cache CSS and JS files
  workbox.routing.registerRoute(
    ({request}) => request.destination === 'style' || request.destination === 'script',
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: 'static-resources',
    })
  );
  
  // Cache API requests (if any) with network-first strategy
  workbox.routing.registerRoute(
    ({url}) => url.origin === self.location.origin && url.pathname.startsWith('/api/'),
    new workbox.strategies.NetworkFirst({
      cacheName: 'api-cache',
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 50,
          maxAgeSeconds: 5 * 60, // 5 Minutes
        }),
      ],
    })
  );
  
  // Precache and update core app files
  workbox.precaching.precacheAndRoute(self.__WB_MANIFEST || []);
  
} else {
  console.log('Boo! Workbox didn\'t load 😬');
}

// Event listeners for service worker lifecycle
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activated!');
  event.waitUntil(self.clients.claim());
});

// Handle push notifications (if needed in future)
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event);
});

// Handle offline navigation
self.addEventListener('fetch', (event) => {
  // For navigation requests, serve the offline page
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request).catch(() => {
        return new Response(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <title>Math Game - Offline</title>
              <style>
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  text-align: center;
                  padding: 40px;
                  background: #f0f8ff;
                }
                .offline-message {
                  max-width: 500px;
                  margin: 0 auto;
                  padding: 30px;
                  background: white;
                  border-radius: 10px;
                  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                h1 {
                  color: #4CAF50;
                }
                p {
                  font-size: 18px;
                  line-height: 1.5;
                }
              </style>
            </head>
            <body>
              <div class="offline-message">
                <h1>🧮 Math Game</h1>
                <p>You are currently offline. The math game will start automatically when you're back online.</p>
                <p>Keep playing and practicing your math skills!</p>
              </div>
            </body>
          </html>
        `, {
          headers: { 'Content-Type': 'text/html' }
        });
      })
    );
  }
});