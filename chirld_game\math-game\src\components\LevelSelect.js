import React from 'react';

const LevelSelect = ({ unlockedLevels, completedLevels, onLevelSelect }) => {
  const MAX_LEVELS = 20;

  const getLevelStatus = (level) => {
    if (completedLevels.includes(level)) {
      return 'completed';
    } else if (level <= unlockedLevels) {
      return 'unlocked';
    } else {
      return 'locked';
    }
  };

  const getLevelIcon = (level) => {
    const status = getLevelStatus(level);
    switch (status) {
      case 'completed':
        return '⭐';
      case 'unlocked':
        return '🔓';
      default:
        return '🔒';
    }
  };

  const getLevelDescription = (level) => {
    if (level <= 3) {
      return 'Simple Addition';
    } else if (level <= 6) {
      return 'Addition & Subtraction';
    } else if (level <= 10) {
      return 'Mixed Practice';
    } else if (level <= 15) {
      return 'Advanced Problems';
    } else {
      return 'Challenge Mode';
    }
  };

  const getDifficultyColor = (level) => {
    if (level <= 3) return 'easy';
    if (level <= 6) return 'medium';
    if (level <= 10) return 'hard';
    if (level <= 15) return 'expert';
    return 'master';
  };

  const handleLevelClick = (level) => {
    const status = getLevelStatus(level);
    if (status !== 'locked') {
      onLevelSelect(level);
    }
  };

  // Check if today's level is available
  const todayLevel = Math.min(unlockedLevels, MAX_LEVELS);

  return (
    <div className="level-select">
      <div className="level-select-header">
        <h2>Choose Your Level</h2>
        <p>Complete one level per day to unlock the next!</p>
      </div>

      <div className="today-level">
        <h3>📅 Today's Level</h3>
        <div 
          className={`level-card today ${getLevelStatus(todayLevel)}`}
          onClick={() => handleLevelClick(todayLevel)}
        >
          <div className="level-number">{todayLevel}</div>
          <div className="level-icon">{getLevelIcon(todayLevel)}</div>
          <div className="level-info">
            <div className="level-title">Level {todayLevel}</div>
            <div className="level-desc">{getLevelDescription(todayLevel)}</div>
          </div>
          <div className={`difficulty-badge ${getDifficultyColor(todayLevel)}`}>
            {getDifficultyColor(todayLevel)}
          </div>
        </div>
      </div>

      <div className="all-levels">
        <h3>All Levels</h3>
        <div className="levels-grid">
          {Array.from({ length: MAX_LEVELS }, (_, i) => {
            const level = i + 1;
            const status = getLevelStatus(level);
            
            return (
              <div
                key={level}
                className={`level-card ${status} ${getDifficultyColor(level)}`}
                onClick={() => handleLevelClick(level)}
                title={status === 'locked' ? 'Complete previous levels to unlock' : `Play Level ${level}`}
              >
                <div className="level-number">{level}</div>
                <div className="level-icon">{getLevelIcon(level)}</div>
                <div className="level-status">
                  {status === 'completed' && <span className="completed-text">✓</span>}
                  {status === 'locked' && <span className="locked-text">🔒</span>}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="level-legend">
        <h4>Legend:</h4>
        <div className="legend-items">
          <div className="legend-item">
            <span className="legend-icon">⭐</span>
            <span>Completed</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon">🔓</span>
            <span>Available</span>
          </div>
          <div className="legend-item">
            <span className="legend-icon">🔒</span>
            <span>Locked</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LevelSelect;
