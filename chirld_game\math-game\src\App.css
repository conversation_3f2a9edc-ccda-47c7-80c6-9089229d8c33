/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Common Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  margin: 5px;
}

.btn-primary {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

/* Menu Screen */
.menu-screen {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.game-header {
  text-align: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.9);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.game-header h1 {
  font-size: 3rem;
  color: #333;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-header p {
  font-size: 1.2rem;
  color: #666;
}

/* Progress Tracker */
.progress-tracker {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.progress-header h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.motivational-message {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card.streak {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.stat-icon {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5rem;
  opacity: 0.7;
}

/* Streak Section */
.streak-section {
  text-align: center;
  margin-bottom: 25px;
}

.streak-message {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  padding: 15px 25px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

/* Activity Calendar */
.activity-calendar {
  margin-bottom: 25px;
}

.activity-calendar h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
}

.calendar-day {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 10px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.calendar-day.completed {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  border-color: #4ecdc4;
}

.calendar-day.incomplete {
  background: #e9ecef;
  color: #6c757d;
}

.day-label {
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.day-indicator {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.day-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
}

.level-badge, .score-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
}

/* Achievements */
.achievements h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.achievement-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.achievement {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 10px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.achievement.earned {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-color: #ffd700;
  color: #333;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.achievement-icon {
  font-size: 1.5rem;
  margin-right: 10px;
}

.achievement-text {
  font-weight: bold;
}

/* Level Select */
.level-select {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.level-select-header {
  text-align: center;
  margin-bottom: 30px;
}

.level-select-header h2 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 10px;
}

.level-select-header p {
  color: #666;
  font-size: 1.1rem;
}

/* Today's Level */
.today-level {
  margin-bottom: 30px;
}

.today-level h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.level-card.today {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  color: white;
  padding: 20px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

.level-card.today:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* All Levels Grid */
.all-levels h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.level-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.level-card:not(.today) {
  background: #f8f9fa;
}

.level-card.unlocked {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  border-color: #4ecdc4;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.level-card.completed {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border-color: #ffd700;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.level-card.locked {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.level-card:not(.locked):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.level-number {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.level-icon {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.level-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 15px;
}

.level-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.level-desc {
  font-size: 0.9rem;
  opacity: 0.8;
}

.level-status {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 1rem;
}

.difficulty-badge {
  position: absolute;
  bottom: 5px;
  right: 5px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
}

.difficulty-badge.easy {
  background: #28a745;
  color: white;
}

.difficulty-badge.medium {
  background: #ffc107;
  color: #333;
}

.difficulty-badge.hard {
  background: #fd7e14;
  color: white;
}

.difficulty-badge.expert {
  background: #dc3545;
  color: white;
}

.difficulty-badge.master {
  background: #6f42c1;
  color: white;
}

/* Level Legend */
.level-legend {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.level-legend h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
}

.legend-items {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 5px;
}

.legend-icon {
  font-size: 1.2rem;
  margin-right: 8px;
}

/* Game Screen */
.game-screen {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.game-screen .game-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 15px 25px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-btn {
  background: linear-gradient(45deg, #6c757d, #495057);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.game-info {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.level-info, .progress-info, .time-info, .score-info {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 8px 15px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9rem;
}

/* Game Content */
.game-content {
  max-width: 800px;
  margin: 0 auto;
}

.problem-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.problem-display {
  margin-bottom: 30px;
}

.problem-text {
  font-size: 3rem;
  color: #333;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.answer-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.answer-input {
  font-size: 2rem;
  padding: 15px 25px;
  border: 3px solid #e9ecef;
  border-radius: 15px;
  text-align: center;
  width: 200px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.answer-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.submit-btn {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Feedback */
.feedback {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.5rem;
  font-weight: bold;
  animation: feedbackPop 0.5s ease-out;
}

.feedback.correct {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.feedback.incorrect {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
  color: white;
}

.feedback-text {
  text-align: center;
}

.feedback-icon {
  font-size: 3rem;
}

@keyframes feedbackPop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Progress Bar */
.progress-bar {
  background: rgba(255, 255, 255, 0.3);
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 20px;
}

.progress-fill {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  height: 100%;
  border-radius: 5px;
  transition: width 0.5s ease;
}

/* Completion Screen */
.completion-screen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.completion-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 50px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: completionPop 0.6s ease-out;
}

.completion-content h2 {
  font-size: 3rem;
  color: #333;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.completion-content p {
  font-size: 1.5rem;
  color: #666;
  margin-bottom: 30px;
}

.completion-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

@keyframes completionPop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-header h1 {
    font-size: 2rem;
  }

  .problem-text {
    font-size: 2rem;
  }

  .answer-input {
    font-size: 1.5rem;
    width: 150px;
    padding: 12px 20px;
  }

  .levels-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .progress-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .game-info {
    justify-content: center;
  }

  .completion-content {
    padding: 30px;
  }

  .completion-content h2 {
    font-size: 2rem;
  }
  
  .problem-container {
    padding: 20px;
  }
  
  .btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* iPad specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .menu-screen {
    padding: 15px;
  }
  
  .game-header {
    padding: 20px;
  }
  
  .progress-tracker {
    padding: 20px;
  }
  
  .level-select {
    padding: 20px;
  }
  
  .problem-container {
    padding: 30px;
  }
  
  .problem-text {
    font-size: 2.5rem;
  }
  
  .answer-input {
    font-size: 1.8rem;
    width: 180px;
  }
  
  /* iPad font optimizations */
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  .game-header h1 {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
  
  .problem-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 600;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn, .level-card, .back-btn, .submit-btn {
    /* Increase touch targets for better touch experience */
    min-height: 44px;
    min-width: 44px;
    padding: 12px 24px;
  }
  
  .level-card {
    min-height: 100px;
  }
  
  .answer-input {
    font-size: 2rem;
    padding: 15px 25px;
    min-height: 60px;
  }
  
  /* Remove hover effects on touch devices */
  .btn-primary:hover,
  .btn-secondary:hover,
  .level-card:not(.locked):hover,
  .back-btn:hover,
  .submit-btn:hover:not(:disabled) {
    transform: none;
    box-shadow: initial;
  }
  
  /* Add touch feedback */
  .btn:active,
  .level-card:active,
  .back-btn:active,
  .submit-btn:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

/* Prevent text selection on touch */
.btn, .level-card, .back-btn, .submit-btn {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Ensure proper viewport handling */
@viewport {
  width: device-width;
  zoom: 1.0;
}
