import React from 'react';

const ProgressTracker = ({ progress }) => {
  const { unlockedLevels, completedLevels, dailyProgress, streak } = progress;

  // Get recent activity (last 7 days)
  const getRecentActivity = () => {
    const activities = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toDateString();
      
      const dayProgress = dailyProgress[dateStr];
      activities.push({
        date: dateStr,
        day: date.toLocaleDateString('en-US', { weekday: 'short' }),
        completed: !!dayProgress?.completed,
        score: dayProgress?.score || 0,
        level: dayProgress?.level || 0
      });
    }
    
    return activities;
  };

  const recentActivity = getRecentActivity();
  const totalCompleted = completedLevels.length;
  const completionRate = totalCompleted > 0 ? Math.round((completedLevels.reduce((sum, level) => {
    const levelProgress = Object.values(dailyProgress).find(p => p.level === level);
    return sum + (levelProgress?.score || 0);
  }, 0) / (totalCompleted * 10)) * 100) : 0;

  const getStreakMessage = () => {
    if (streak === 0) return "Start your streak today! 🌟";
    if (streak === 1) return "Great start! Keep it up! 🔥";
    if (streak < 7) return `${streak} days strong! 💪`;
    if (streak < 30) return `Amazing ${streak}-day streak! 🚀`;
    return `Incredible ${streak}-day streak! You're a math champion! 🏆`;
  };

  const getMotivationalMessage = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning! Ready for some math fun? ☀️";
    if (hour < 17) return "Good afternoon! Time for math practice! 📚";
    return "Good evening! Let's solve some problems! 🌙";
  };

  return (
    <div className="progress-tracker">
      <div className="progress-header">
        <h3>Your Progress</h3>
        <p className="motivational-message">{getMotivationalMessage()}</p>
      </div>

      <div className="progress-stats">
        <div className="stat-card">
          <div className="stat-number">{totalCompleted}</div>
          <div className="stat-label">Levels Completed</div>
          <div className="stat-icon">🎯</div>
        </div>

        <div className="stat-card">
          <div className="stat-number">{unlockedLevels}</div>
          <div className="stat-label">Levels Unlocked</div>
          <div className="stat-icon">🔓</div>
        </div>

        <div className="stat-card">
          <div className="stat-number">{completionRate}%</div>
          <div className="stat-label">Average Score</div>
          <div className="stat-icon">📊</div>
        </div>

        <div className="stat-card streak">
          <div className="stat-number">{streak}</div>
          <div className="stat-label">Day Streak</div>
          <div className="stat-icon">🔥</div>
        </div>
      </div>

      <div className="streak-section">
        <div className="streak-message">
          {getStreakMessage()}
        </div>
      </div>

      <div className="activity-calendar">
        <h4>This Week's Activity</h4>
        <div className="calendar-grid">
          {recentActivity.map((day, index) => (
            <div 
              key={index} 
              className={`calendar-day ${day.completed ? 'completed' : 'incomplete'}`}
              title={day.completed ? `Completed Level ${day.level} with ${day.score}/10 score` : 'No activity'}
            >
              <div className="day-label">{day.day}</div>
              <div className="day-indicator">
                {day.completed ? '✅' : '⭕'}
              </div>
              {day.completed && (
                <div className="day-details">
                  <div className="level-badge">L{day.level}</div>
                  <div className="score-badge">{day.score}/10</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="achievements">
        <h4>Achievements</h4>
        <div className="achievement-list">
          {totalCompleted >= 1 && (
            <div className="achievement earned">
              <span className="achievement-icon">🌟</span>
              <span className="achievement-text">First Level Complete!</span>
            </div>
          )}
          {totalCompleted >= 5 && (
            <div className="achievement earned">
              <span className="achievement-icon">🎯</span>
              <span className="achievement-text">5 Levels Mastered!</span>
            </div>
          )}
          {streak >= 3 && (
            <div className="achievement earned">
              <span className="achievement-icon">🔥</span>
              <span className="achievement-text">3-Day Streak!</span>
            </div>
          )}
          {streak >= 7 && (
            <div className="achievement earned">
              <span className="achievement-icon">💪</span>
              <span className="achievement-text">Week Warrior!</span>
            </div>
          )}
          {completionRate >= 80 && totalCompleted >= 3 && (
            <div className="achievement earned">
              <span className="achievement-icon">🏆</span>
              <span className="achievement-text">High Scorer!</span>
            </div>
          )}
          {totalCompleted >= 10 && (
            <div className="achievement earned">
              <span className="achievement-icon">🚀</span>
              <span className="achievement-text">Math Explorer!</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
