import React, { useState, useEffect } from 'react';
import './App.css';

// Game components
import GameScreen from './components/GameScreen';
import LevelSelect from './components/LevelSelect';
import ProgressTracker from './components/ProgressTracker';

function App() {
  const [currentLevel, setCurrentLevel] = useState(1);
  const [gameState, setGameState] = useState('menu'); // 'menu', 'playing', 'completed'
  const [playerProgress, setPlayerProgress] = useState({
    unlockedLevels: 1,
    completedLevels: [],
    dailyProgress: {},
    streak: 0
  });

  // Load progress from localStorage on component mount
  useEffect(() => {
    const savedProgress = localStorage.getItem('mathGameProgress');
    if (savedProgress) {
      setPlayerProgress(JSON.parse(savedProgress));
    }
  }, []);

  // Save progress to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('mathGameProgress', JSON.stringify(playerProgress));
  }, [playerProgress]);

  // Check daily progress and unlock new levels
  useEffect(() => {
    const today = new Date().toDateString();
    const todayProgress = playerProgress.dailyProgress[today];

    if (todayProgress && todayProgress.completed && !playerProgress.completedLevels.includes(currentLevel)) {
      // If today's level is completed, unlock next level
      setPlayerProgress(prev => ({
        ...prev,
        unlockedLevels: Math.max(prev.unlockedLevels, currentLevel + 1),
        completedLevels: [...prev.completedLevels, currentLevel]
      }));
    }
  }, [currentLevel, playerProgress.dailyProgress, playerProgress.completedLevels]);

  const handleLevelComplete = (score, timeSpent) => {
    const today = new Date().toDateString();

    setPlayerProgress(prev => {
      const newProgress = { ...prev };

      // Update daily progress
      newProgress.dailyProgress[today] = {
        level: currentLevel,
        score: score,
        timeSpent: timeSpent,
        completed: true,
        date: today
      };

      // Add to completed levels if not already there
      if (!newProgress.completedLevels.includes(currentLevel)) {
        newProgress.completedLevels.push(currentLevel);
      }

      // Unlock next level
      newProgress.unlockedLevels = Math.max(newProgress.unlockedLevels, currentLevel + 1);

      // Update streak
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toDateString();

      if (newProgress.dailyProgress[yesterdayStr]?.completed) {
        newProgress.streak = (newProgress.streak || 0) + 1;
      } else {
        newProgress.streak = 1;
      }

      return newProgress;
    });

    setGameState('completed');
  };

  const startGame = (level) => {
    setCurrentLevel(level);
    setGameState('playing');
  };

  const returnToMenu = () => {
    setGameState('menu');
  };

  const renderCurrentScreen = () => {
    switch (gameState) {
      case 'playing':
        return (
          <GameScreen
            level={currentLevel}
            onComplete={handleLevelComplete}
            onBack={returnToMenu}
          />
        );
      case 'completed':
        return (
          <div className="completion-screen">
            <div className="completion-content">
              <h2>🎉 Great Job! 🎉</h2>
              <p>You completed Level {currentLevel}!</p>
              <div className="completion-buttons">
                <button onClick={returnToMenu} className="btn btn-primary">
                  Back to Levels
                </button>
                {playerProgress.unlockedLevels > currentLevel && (
                  <button
                    onClick={() => startGame(currentLevel + 1)}
                    className="btn btn-secondary"
                  >
                    Next Level
                  </button>
                )}
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="menu-screen">
            <header className="game-header">
              <h1>🧮 Math Adventure 🧮</h1>
              <p>Fun math practice for 2nd graders!</p>
            </header>

            <ProgressTracker progress={playerProgress} />

            <LevelSelect
              unlockedLevels={playerProgress.unlockedLevels}
              completedLevels={playerProgress.completedLevels}
              onLevelSelect={startGame}
            />
          </div>
        );
    }
  };

  return (
    <div className="App">
      {renderCurrentScreen()}
    </div>
  );
}

export default App;
