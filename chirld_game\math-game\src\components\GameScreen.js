import React, { useState, useEffect } from 'react';

const GameScreen = ({ level, onComplete, onBack }) => {
  const [currentProblem, setCurrentProblem] = useState(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState(0);
  const [problemCount, setProblemCount] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [gameStartTime] = useState(Date.now());
  const [showFeedback, setShowFeedback] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  const PROBLEMS_PER_LEVEL = 10;

  // Generate a new math problem based on level
  const generateProblem = () => {
    let num1, num2, operation, answer;
    
    // Level-based difficulty
    const maxNumber = Math.min(20 + (level - 1) * 10, 100);
    
    if (level <= 3) {
      // Early levels: simple addition
      num1 = Math.floor(Math.random() * maxNumber) + 1;
      num2 = Math.floor(Math.random() * maxNumber) + 1;
      operation = '+';
      answer = num1 + num2;
    } else if (level <= 6) {
      // Mid levels: addition and subtraction
      operation = Math.random() < 0.6 ? '+' : '-';
      if (operation === '+') {
        num1 = Math.floor(Math.random() * maxNumber) + 1;
        num2 = Math.floor(Math.random() * maxNumber) + 1;
        answer = num1 + num2;
      } else {
        // For subtraction, ensure positive result
        num1 = Math.floor(Math.random() * maxNumber) + 10;
        num2 = Math.floor(Math.random() * (num1 - 1)) + 1;
        answer = num1 - num2;
      }
    } else {
      // Higher levels: mixed operations
      const operations = ['+', '-'];
      operation = operations[Math.floor(Math.random() * operations.length)];
      
      if (operation === '+') {
        num1 = Math.floor(Math.random() * maxNumber) + 1;
        num2 = Math.floor(Math.random() * maxNumber) + 1;
        answer = num1 + num2;
      } else {
        num1 = Math.floor(Math.random() * maxNumber) + 10;
        num2 = Math.floor(Math.random() * (num1 - 1)) + 1;
        answer = num1 - num2;
      }
    }

    return {
      num1,
      num2,
      operation,
      answer,
      question: `${num1} ${operation} ${num2} = ?`
    };
  };

  // Initialize first problem
  useEffect(() => {
    setCurrentProblem(generateProblem());
  }, [level]); // eslint-disable-line react-hooks/exhaustive-deps

  // Update time spent
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - gameStartTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [gameStartTime]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!userAnswer.trim()) return;

    const userNum = parseInt(userAnswer);
    const correct = userNum === currentProblem.answer;
    
    setIsCorrect(correct);
    setShowFeedback(true);
    
    if (correct) {
      setScore(score + 1);
      setFeedback(getPositiveFeedback());
    } else {
      setFeedback(`Not quite! The answer is ${currentProblem.answer}. Try the next one!`);
    }

    // Auto-advance after showing feedback
    setTimeout(() => {
      setShowFeedback(false);
      setUserAnswer('');
      setProblemCount(problemCount + 1);
      
      if (problemCount + 1 >= PROBLEMS_PER_LEVEL) {
        // Level complete
        onComplete(score + (correct ? 1 : 0), timeSpent);
      } else {
        // Next problem
        setCurrentProblem(generateProblem());
      }
    }, 2000);
  };

  const getPositiveFeedback = () => {
    const messages = [
      "Excellent! 🌟",
      "Great job! 🎉",
      "Perfect! 👏",
      "Awesome! 🚀",
      "Fantastic! ⭐",
      "Well done! 🎊",
      "Amazing! 🏆",
      "Super! 💫"
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentProblem) return <div>Loading...</div>;

  return (
    <div className="game-screen">
      <div className="game-header">
        <button onClick={onBack} className="back-btn">← Back</button>
        <div className="game-info">
          <span className="level-info">Level {level}</span>
          <span className="progress-info">
            {problemCount + 1} / {PROBLEMS_PER_LEVEL}
          </span>
          <span className="time-info">⏱️ {formatTime(timeSpent)}</span>
          <span className="score-info">Score: {score}</span>
        </div>
      </div>

      <div className="game-content">
        <div className="problem-container">
          <div className="problem-display">
            <h2 className="problem-text">{currentProblem.question}</h2>
          </div>

          {!showFeedback ? (
            <form onSubmit={handleSubmit} className="answer-form">
              <input
                type="number"
                value={userAnswer}
                onChange={(e) => setUserAnswer(e.target.value)}
                placeholder="Your answer"
                className="answer-input"
                autoFocus
                inputMode="numeric"
                pattern="[0-9]*"
                autoComplete="off"
              />
              <button type="submit" className="submit-btn" disabled={!userAnswer.trim()}>
                Submit
              </button>
            </form>
          ) : (
            <div className={`feedback ${isCorrect ? 'correct' : 'incorrect'}`}>
              <div className="feedback-text">{feedback}</div>
              <div className="feedback-icon">
                {isCorrect ? '✅' : '❌'}
              </div>
            </div>
          )}
        </div>

        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${((problemCount + 1) / PROBLEMS_PER_LEVEL) * 100}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default GameScreen;
